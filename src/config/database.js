import { Sequelize  } from 'sequelize';
import logger from './logger.js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env' });

// User Database Configuration
const userDbConfig = {
  host: process.env.USER_DB_HOST,
  port: parseInt(process.env.USER_DB_PORT || '3306'),
  database: process.env.USER_DB_NAME || 'infini_ai_users',
  username: process.env.USER_DB_USERNAME || 'inf_ai_user',
  password: process.env.USER_DB_PASSWORD || 'inf_ai_user',
};

// Chat Database Configuration
const chatDbConfig = {
  host: process.env.CHAT_DB_HOST,
  port: parseInt(process.env.CHAT_DB_PORT || '3306'),
  database: process.env.CHAT_DB_NAME || 'infini_ai_user_chat_recs',
  username: process.env.CHAT_DB_USERNAME || 'inf_ai_chat_recs',
  password: process.env.CHAT_DB_PASSWORD || 'inf_ai_chat_recs',
};

// Common Sequelize options
const commonOptions = {
  dialect: 'mysql',
  logging: (msg) => logger.debug(msg),
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
};

// Create Sequelize instances
const userDatabase = new Sequelize(
  userDbConfig.database,
  userDbConfig.username,
  userDbConfig.password,
  {
    ...commonOptions,
    host: userDbConfig.host,
    port: userDbConfig.port,
  }
);

const chatDatabase = new Sequelize(
  chatDbConfig.database,
  chatDbConfig.username,
  chatDbConfig.password,
  {
    ...commonOptions,
    host: chatDbConfig.host,
    port: chatDbConfig.port,
  }
);

/**
 * Database Manager Class
 * Handles database connections, synchronization, and management
 */
class DatabaseManager {
  /**
   * Connect to user database
   * @returns {Promise<void>}
   */
  static async connectUserDatabase() {
    try {
      await userDatabase.authenticate();
      logger.info('User database connection established successfully');
    } catch (error) {
      logger.error('Unable to connect to user database:', error);
      throw error;
    }
  }

  /**
   * Connect to chat database
   * @returns {Promise<void>}
   */
  static async connectChatDatabase() {
    try {
      await chatDatabase.authenticate();
      logger.info('Chat database connection established successfully');
    } catch (error) {
      logger.error('Unable to connect to chat database:', error);
      throw error;
    }
  }

  /**
   * Connect to all databases
   * @returns {Promise<void>}
   */
  static async connectAllDatabases() {
    await Promise.all([
      this.connectUserDatabase(),
      this.connectChatDatabase(),
    ]);
  }

  /**
   * Synchronize user database
   * @param {boolean} force - Whether to force recreate tables
   * @returns {Promise<void>}
   */
  static async syncUserDatabase(force = false) {
    try {
      await userDatabase.sync({ force });
      logger.info('User database synchronized successfully');
    } catch (error) {
      logger.error('Error synchronizing user database:', error);
      throw error;
    }
  }

  /**
   * Synchronize chat database
   * @param {boolean} force - Whether to force recreate tables
   * @returns {Promise<void>}
   */
  static async syncChatDatabase(force = false) {
    try {
      await chatDatabase.sync({ force });
      logger.info('Chat database synchronized successfully');
    } catch (error) {
      logger.error('Error synchronizing chat database:', error);
      throw error;
    }
  }

  /**
   * Synchronize all databases
   * @param {boolean} force - Whether to force recreate tables
   * @returns {Promise<void>}
   */
  static async syncAllDatabases(force = false) {
    await Promise.all([
      this.syncUserDatabase(force),
      this.syncChatDatabase(force),
    ]);
  }

  /**
   * Close all database connections
   * @returns {Promise<void>}
   */
  static async closeAllConnections() {
    await Promise.all([
      userDatabase.close(),
      chatDatabase.close(),
    ]);
    logger.info('All database connections closed');
  }
}

export { userDatabase,
  chatDatabase,
  DatabaseManager,
 };

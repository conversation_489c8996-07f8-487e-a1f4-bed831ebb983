import { DataTypes, Model, Op  } from 'sequelize';
import { userDatabase  } from '../../config/database.js';
import { User } from './User.js';
import { EncryptionUtil  } from '../../utils/encryption.js';

/**
 * UserOTP Model
 * Handles OTP generation and verification for user authentication
 */
class UserOTP extends Model {
  /**
   * Check if OTP is expired
   * @returns {boolean} True if OTP is expired
   */
  isExpired() {
    return new Date() > this.expiresAt;
  }

  /**
   * Mark OTP as used
   * @returns {Promise<void>}
   */
  async markAsUsed() {
    this.isUsed = true;
    await this.save();
  }

  /**
   * Create a new OTP for user
   * @param {string} userId - User ID
   * @param {number} [expiryMinutes=5] - OTP expiry in minutes
   * @returns {Promise<UserOTP>} Created OTP instance
   */
  static async createOTP(userId, expiryMinutes = 5) {

    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

    return UserOTP.create({
      id: EncryptionUtil.generateUUID(),
      userId,
      otp: EncryptionUtil.generateOTP(6),
      expiresAt,
      isUsed: false,
    });
  }

  /**
   * Find valid OTP for user
   * @param {string} userId - User ID
   * @param {string} otp - OTP code
   * @returns {Promise<UserOTP|null>} Valid OTP instance or null
   */
  static async findValidOTP(userId, otp) {
    return UserOTP.findOne({
      where: {
        userId,
        otp,
        isUsed: false,
        expiresAt: {
          [Op.gt]: new Date(),
        },
      },
    });
  }

  /**
   * Clean up expired OTPs
   * @returns {Promise<number>} Number of deleted OTPs
   */
  static async cleanupExpiredOTPs() {
    return UserOTP.destroy({
      where: {
        [Op.or]: [
          {
            expiresAt: {
              [Op.lt]: new Date(),
            },
          },
          {
            isUsed: true,
          },
        ],
      },
    });
  }

  /**
   * Invalidate all OTPs for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Number of invalidated OTPs
   */
  static async invalidateUserOTPs(userId) {
    return UserOTP.update(
      { isUsed: true },
      {
        where: {
          userId,
          isUsed: false,
        },
      }
    );
  }

  /**
   * Get latest OTP for user
   * @param {string} userId - User ID
   * @returns {Promise<UserOTP|null>} Latest OTP instance or null
   */
  static async getLatestOTP(userId) {
    return UserOTP.findOne({
      where: { userId },
      order: [['createdAt', 'DESC']],
    });
  }
}

UserOTP.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      // Note: Foreign key reference will be set up in associations
    },
    otp: {
      type: DataTypes.STRING(10),
      allowNull: false,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    isUsed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: userDatabase,
    modelName: 'UserOTP',
    tableName: 'user_otps',
    timestamps: false,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['otp'],
      },
      {
        fields: ['expires_at'],
      },
      {
        fields: ['is_used'],
      },
    ],
  }
);

// Define associations
User.hasMany(UserOTP, { foreignKey: 'user_id', as: 'otps' });
UserOTP.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

export { UserOTP  };

import { Pinecone } from '@pinecone-database/pinecone';
import logger from '../config/logger.js';

export class PineconeService {
  static client = null;
  static index = null;

  /**
   * Initialize Pinecone client
   * @returns {Promise<void>}
   */
  static async initialize() {
    try {
      if (!process.env.PINECONE_API_KEY) {
        logger.warn('Pinecone API key not found. Pinecone service will be disabled.');
        return;
      }

      this.client = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY,
      });

      const indexName = process.env.PINECONE_INDEX_NAME || 'theinfini-ai-chat';
      this.index = this.client.index(indexName);

      logger.info('Pinecone service initialized successfully');
    } catch (error) {
      logger.error('Error initializing Pinecone service:', error);
      throw error;
    }
  }

  /**
   * Check if Pinecone is available
   * @returns {boolean} Whether Pinecone is available
   */
  static isAvailable() {
    return !!this.client && !!this.index;
  }

  /**
   * Generate embedding for text using OpenAI
   * @param {string} text - Text to generate embedding for
   * @returns {Promise<Array<number>>} Embedding vector
   */
  static async generateEmbedding(text) {
    try {
      // For now, we'll use a simple text-to-vector conversion
      // In production, you'd want to use OpenAI's embedding API
      const { createHash } = await import('crypto');
      const hash = createHash('sha256').update(text).digest('hex');
      
      // Convert hash to a 1536-dimensional vector (OpenAI embedding size)
      const vector = [];
      for (let i = 0; i < 1536; i++) {
        const charCode = hash.charCodeAt(i % hash.length);
        vector.push((charCode / 255) * 2 - 1); // Normalize to [-1, 1]
      }
      
      return vector;
    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw error;
    }
  }

  /**
   * Store chat message in Pinecone
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} messageId - Message ID
   * @param {string} message - User message
   * @param {string} response - Assistant response
   * @param {Object} [metadata] - Additional metadata
   * @returns {Promise<void>}
   */
  static async storeMessage(
    projectId,
    threadId,
    messageId,
    message,
    response,
    metadata = {}
  ) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping message storage');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      const combinedText = `User: ${message}\nAssistant: ${response}`;
      const embedding = await this.generateEmbedding(combinedText);

      await this.index.namespace(namespace).upsert([
        {
          id: messageId,
          values: embedding,
          metadata: {
            projectId,
            threadId,
            message,
            response,
            timestamp: new Date().toISOString(),
            ...metadata,
          },
        },
      ]);

      logger.debug(`Stored message in Pinecone: ${messageId}`);
    } catch (error) {
      logger.error('Error storing message in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Search for similar messages in project context
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} query - Search query
   * @param {number} [topK] - Number of results to return
   * @returns {Promise<Array>} Search results
   */
  static async searchSimilarMessages(
    projectId,
    threadId,
    query,
    topK = 5
  ) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty results');
        return [];
      }

      const namespace = `${projectId}_${threadId}`;
      const queryEmbedding = await this.generateEmbedding(query);

      const searchResults = await this.index.namespace(namespace).query({
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
      });

      return searchResults.matches || [];
    } catch (error) {
      logger.error('Error searching similar messages in Pinecone:', error);
      return [];
    }
  }

  /**
   * Delete all messages for a thread
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @returns {Promise<void>}
   */
  static async deleteThreadMessages(projectId, threadId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping thread deletion');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      
      // Delete all vectors in the namespace
      await this.index.namespace(namespace).deleteAll();

      logger.info(`Deleted all messages for thread ${threadId} in project ${projectId}`);
    } catch (error) {
      logger.error('Error deleting thread messages from Pinecone:', error);
      throw error;
    }
  }

  /**
   * Delete all messages for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<void>}
   */
  static async deleteProjectMessages(projectId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping project deletion');
        return;
      }

      // Note: This is a simplified implementation
      // In a real scenario, you might need to list all namespaces for the project
      // and delete them individually
      
      logger.warn(`Project deletion for ${projectId} - manual cleanup may be required`);
    } catch (error) {
      logger.error('Error deleting project messages from Pinecone:', error);
      throw error;
    }
  }

  /**
   * Get namespace statistics
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @returns {Promise<Object>} Namespace statistics
   */
  static async getNamespaceStats(projectId, threadId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty stats');
        return { vectorCount: 0 };
      }

      const namespace = `${projectId}_${threadId}`;
      const stats = await this.index.describeIndexStats();
      
      return {
        vectorCount: stats.namespaces?.[namespace]?.vectorCount || 0,
        totalVectors: stats.totalVectorCount || 0,
      };
    } catch (error) {
      logger.error('Error getting namespace stats from Pinecone:', error);
      return { vectorCount: 0 };
    }
  }

  /**
   * Update message metadata
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} messageId - Message ID
   * @param {Object} metadata - New metadata
   * @returns {Promise<void>}
   */
  static async updateMessageMetadata(projectId, threadId, messageId, metadata) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping metadata update');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      
      // Fetch existing vector
      const fetchResult = await this.index.namespace(namespace).fetch([messageId]);
      const existingVector = fetchResult.vectors?.[messageId];
      
      if (!existingVector) {
        logger.warn(`Vector ${messageId} not found for metadata update`);
        return;
      }

      // Update with new metadata
      await this.index.namespace(namespace).upsert([
        {
          id: messageId,
          values: existingVector.values,
          metadata: {
            ...existingVector.metadata,
            ...metadata,
            updatedAt: new Date().toISOString(),
          },
        },
      ]);

      logger.debug(`Updated metadata for message: ${messageId}`);
    } catch (error) {
      logger.error('Error updating message metadata in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Batch store multiple messages
   * @param {Array} messages - Array of message objects
   * @returns {Promise<void>}
   */
  static async batchStoreMessages(messages) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping batch storage');
        return;
      }

      const batchSize = 100; // Pinecone batch limit
      
      for (let i = 0; i < messages.length; i += batchSize) {
        const batch = messages.slice(i, i + batchSize);
        
        for (const msg of batch) {
          await this.storeMessage(
            msg.projectId,
            msg.threadId,
            msg.messageId,
            msg.message,
            msg.response,
            msg.metadata
          );
        }
      }

      logger.info(`Batch stored ${messages.length} messages in Pinecone`);
    } catch (error) {
      logger.error('Error batch storing messages in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Get Pinecone service health status
   * @returns {Promise<Object>} Health status
   */
  static async getHealthStatus() {
    try {
      if (!this.isAvailable()) {
        return {
          status: 'unavailable',
          message: 'Pinecone client not initialized',
        };
      }

      const stats = await this.index.describeIndexStats();
      
      return {
        status: 'healthy',
        totalVectors: stats.totalVectorCount || 0,
        dimension: stats.dimension || 0,
        namespaces: Object.keys(stats.namespaces || {}),
      };
    } catch (error) {
      logger.error('Error checking Pinecone health:', error);
      return {
        status: 'error',
        message: error.message,
      };
    }
  }
}

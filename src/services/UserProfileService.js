import { UserProfile } from '../models/user/index.js';
import logger from '../config/logger.js';

export class UserProfileService {
  /**
   * Initialize user profile (called when user is first verified)
   * @param {string} userId - User ID
   * @param {Object} [data] - Initial profile data
   * @param {string} [data.firstName] - First name
   * @param {string} [data.lastName] - Last name
   * @param {string} [data.profilePicture] - Profile picture URL
   * @param {string} [data.plan] - User plan
   * @returns {Promise<Object>} User profile
   */
  static async initializeUserProfile(userId, data) {
    try {
      // Check if user already has a profile
      const existingProfile = await UserProfile.findByUserId(userId);
      if (existingProfile) {
        logger.info(`User ${userId} already has a profile`);
        return existingProfile;
      }

      // Create new profile
      const userProfile = await UserProfile.createUserProfile({
        userId,
        firstName: data?.firstName,
        lastName: data?.lastName,
        profilePicture: data?.profilePicture,
        plan: data?.plan || 'FREE',
      });

      logger.info(`Initialized profile for user ${userId}`);
      return userProfile;
    } catch (error) {
      logger.error('Error initializing user profile:', error);
      throw error;
    }
  }

  /**
   * Get user profile
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} User profile or null
   */
  static async getUserProfile(userId) {
    try {
      return await UserProfile.findByUserId(userId);
    } catch (error) {
      logger.error('Error getting user profile:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} data - Profile data to update
   * @param {string} [data.firstName] - First name
   * @param {string} [data.lastName] - Last name
   * @param {string} [data.profilePicture] - Profile picture URL
   * @returns {Promise<Object>} Updated user profile
   */
  static async updateUserProfile(userId, data) {
    try {
      const [userProfile] = await UserProfile.findOrCreateByUserId(userId);
      await userProfile.updateProfile(data);
      
      logger.info(`Updated profile for user ${userId}`);
      return userProfile;
    } catch (error) {
      logger.error('Error updating user profile:', error);
      throw error;
    }
  }

  /**
   * Update user plan
   * @param {string} userId - User ID
   * @param {string} plan - New plan ('FREE' | 'PREMIUM' | 'ENTERPRISE')
   * @returns {Promise<Object>} Updated user profile
   */
  static async updateUserPlan(userId, plan) {
    try {
      const [userProfile] = await UserProfile.findOrCreateByUserId(userId);
      await userProfile.updatePlan(plan);
      
      logger.info(`Updated plan to ${plan} for user ${userId}`);
      return userProfile;
    } catch (error) {
      logger.error('Error updating user plan:', error);
      throw error;
    }
  }

  /**
   * Get user profile with credits
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Profile and credits data
   */
  static async getUserProfileWithCredits(userId) {
    try {
      const { CreditService } = await import('./CreditService.js');
      
      const [profile, credits] = await Promise.all([
        this.getUserProfile(userId),
        CreditService.getUserCredits(userId),
      ]);

      return { profile, credits };
    } catch (error) {
      logger.error('Error getting user profile with credits:', error);
      throw error;
    }
  }

  /**
   * Get user profile with full details (including credits and stats)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Complete profile data
   */
  static async getFullUserProfile(userId) {
    try {
      const { CreditService } = await import('./CreditService.js');
      
      const [profile, creditStats] = await Promise.all([
        this.getUserProfile(userId),
        CreditService.getCreditStats(userId),
      ]);

      return {
        profile,
        credits: creditStats,
      };
    } catch (error) {
      logger.error('Error getting full user profile:', error);
      throw error;
    }
  }

  /**
   * Delete user profile
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async deleteUserProfile(userId) {
    try {
      const userProfile = await UserProfile.findByUserId(userId);
      if (userProfile) {
        await userProfile.destroy();
        logger.info(`Deleted profile for user ${userId}`);
      }
    } catch (error) {
      logger.error('Error deleting user profile:', error);
      throw error;
    }
  }

  /**
   * Get users by plan
   * @param {string} plan - Plan type
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} Users with specified plan
   */
  static async getUsersByPlan(plan, limit = 50, offset = 0) {
    try {
      return await UserProfile.findAll({
        where: { plan },
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });
    } catch (error) {
      logger.error('Error getting users by plan:', error);
      throw error;
    }
  }

  /**
   * Get profile statistics
   * @returns {Promise<Object>} Profile statistics
   */
  static async getProfileStats() {
    try {
      const [totalProfiles, freeUsers, premiumUsers, enterpriseUsers] = await Promise.all([
        UserProfile.count(),
        UserProfile.count({ where: { plan: 'FREE' } }),
        UserProfile.count({ where: { plan: 'PREMIUM' } }),
        UserProfile.count({ where: { plan: 'ENTERPRISE' } }),
      ]);

      return {
        totalProfiles,
        planDistribution: {
          FREE: freeUsers,
          PREMIUM: premiumUsers,
          ENTERPRISE: enterpriseUsers,
        },
      };
    } catch (error) {
      logger.error('Error getting profile stats:', error);
      throw error;
    }
  }

  /**
   * Search profiles by name
   * @param {string} searchTerm - Search term
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} Matching profiles
   */
  static async searchProfiles(searchTerm, limit = 20, offset = 0) {
    try {
      const { Op } = await import('sequelize');

      return await UserProfile.findAll({
        where: {
          [Op.or]: [
            {
              firstName: {
                [Op.like]: `%${searchTerm}%`
              }
            },
            {
              lastName: {
                [Op.like]: `%${searchTerm}%`
              }
            }
          ]
        },
        limit,
        offset,
        order: [['updatedAt', 'DESC']],
      });
    } catch (error) {
      logger.error('Error searching profiles:', error);
      throw error;
    }
  }

  /**
   * Bulk update user plans
   * @param {Array} updates - Array of {userId, plan} objects
   * @returns {Promise<void>}
   */
  static async bulkUpdatePlans(updates) {
    try {
      for (const { userId, plan } of updates) {
        await this.updateUserPlan(userId, plan);
      }
      logger.info(`Bulk updated plans for ${updates.length} users`);
    } catch (error) {
      logger.error('Error bulk updating plans:', error);
      throw error;
    }
  }

  /**
   * Get recent profile updates
   * @param {number} [limit] - Limit results
   * @returns {Promise<Array>} Recently updated profiles
   */
  static async getRecentUpdates(limit = 10) {
    try {
      return await UserProfile.findAll({
        limit,
        order: [['updatedAt', 'DESC']],
      });
    } catch (error) {
      logger.error('Error getting recent profile updates:', error);
      throw error;
    }
  }

  /**
   * Validate profile data
   * @param {Object} data - Profile data to validate
   * @returns {Object} Validation result
   */
  static validateProfileData(data) {
    const errors = [];

    if (data.firstName && typeof data.firstName !== 'string') {
      errors.push('First name must be a string');
    }

    if (data.lastName && typeof data.lastName !== 'string') {
      errors.push('Last name must be a string');
    }

    if (data.profilePicture && typeof data.profilePicture !== 'string') {
      errors.push('Profile picture must be a valid URL string');
    }

    if (data.plan && !['FREE', 'PREMIUM', 'ENTERPRISE'].includes(data.plan)) {
      errors.push('Plan must be one of: FREE, PREMIUM, ENTERPRISE');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

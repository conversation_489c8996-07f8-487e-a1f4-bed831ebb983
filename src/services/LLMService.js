import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { LLM_MODELS, DEFAULT_LLM_CONFIG } from '../utils/constants.js';
import logger from '../config/logger.js';

export class LLMService {
  static models = new Map();

  /**
   * Initialize LLM models
   */
  static initialize() {
    try {
      // Initialize OpenAI models
      if (process.env.OPENAI_API_KEY) {
        this.initializeOpenAIModels();
      }

      // Initialize Anthropic models
      if (process.env.ANTHROPIC_API_KEY) {
        this.initializeAnthropicModels();
      }

      logger.info('LLM Service initialized successfully');
    } catch (error) {
      logger.error('Error initializing LLM Service:', error);
      throw error;
    }
  }

  /**
   * Initialize OpenAI models
   */
  static initializeOpenAIModels() {
    const openAIModels = Object.values(LLM_MODELS.OPENAI);

    openAIModels.forEach(modelName => {
      const model = new ChatOpenAI({
        modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens,
        openAIApiKey: process.env.OPENAI_API_KEY,
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized OpenAI model: ${modelName}`);
    });
  }

  /**
   * Initialize Anthropic models
   */
  static initializeAnthropicModels() {
    const anthropicModels = Object.values(LLM_MODELS.ANTHROPIC);

    anthropicModels.forEach(modelName => {
      const model = new ChatAnthropic({
        modelName,
        temperature: DEFAULT_LLM_CONFIG.temperature,
        maxTokens: DEFAULT_LLM_CONFIG.maxTokens,
        anthropicApiKey: process.env.ANTHROPIC_API_KEY,
      });

      this.models.set(modelName, model);
      logger.debug(`Initialized Anthropic model: ${modelName}`);
    });
  }

  /**
   * Get LLM model by name
   */
  static getModel(modelName) {
    const defaultModel = process.env.DEFAULT_LLM_MODEL || LLM_MODELS.OPENAI.GPT_3_5_TURBO;
    const targetModel = modelName || defaultModel;

    const model = this.models.get(targetModel);
    if (!model) {
      throw new Error(`LLM model '${targetModel}' not found or not initialized`);
    }

    return model;
  }

  /**
   * Create a custom model with specific configuration
   * @param {Object} config - LLM configuration object
   * @param {string} config.model - Model name
   * @param {number} [config.temperature] - Temperature setting
   * @param {number} [config.maxTokens] - Max tokens setting
   * @param {string} [config.apiKey] - API key
   * @returns {Object} LLM model instance
   */
  static createCustomModel(config) {
    const provider = this.detectProvider(config.model);

    switch (provider) {
      case 'OPENAI':
        return new ChatOpenAI({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          openAIApiKey: config.apiKey || process.env.OPENAI_API_KEY,
        });

      case 'ANTHROPIC':
        return new ChatAnthropic({
          modelName: config.model,
          temperature: config.temperature || DEFAULT_LLM_CONFIG.temperature,
          maxTokens: config.maxTokens || DEFAULT_LLM_CONFIG.maxTokens,
          anthropicApiKey: config.apiKey || process.env.ANTHROPIC_API_KEY,
        });

      default:
        throw new Error(`Unsupported LLM provider for model: ${config.model}`);
    }
  }

  /**
   * Generate chat response (non-streaming)
   * @param {string} message - User message
   * @param {string} [modelName] - Model name to use
   * @param {string} [systemPrompt] - System prompt
   * @param {Array} [conversationHistory] - Previous conversation
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Promise<string>} Generated response
   */
  static async generateResponse(
    message,
    modelName,
    systemPrompt,
    conversationHistory,
    attachedFile
  ) {
    try {
      const model = this.getModel(modelName);
      const messages = this.buildMessages(message, systemPrompt, conversationHistory, attachedFile);

      const response = await model.invoke(messages);

      logger.info(`LLM response generated using model: ${modelName || 'default'}`);
      return response.content;
    } catch (error) {
      logger.error('Error generating LLM response:', error);
      throw new Error('Failed to generate response from LLM');
    }
  }

  /**
   * Generate multimodal chat response (non-streaming)
   * @param {string} message - User message
   * @param {Object} attachedFile - Attached file data
   * @param {string} [modelName] - Model name to use
   * @param {string} [systemPrompt] - System prompt
   * @param {Array} [conversationHistory] - Previous conversation
   * @returns {Promise<string>} Generated response
   */
  static async generateMultimodalResponse(
    message,
    attachedFile,
    modelName,
    systemPrompt,
    conversationHistory
  ) {
    try {
      // Use GPT-4 Vision for multimodal inputs by default
      const multimodalModel = modelName && this.supportsMultimodal(modelName)
        ? modelName
        : LLM_MODELS.OPENAI.GPT_4_VISION_PREVIEW;

      const model = this.getModel(multimodalModel);
      const messages = this.buildMessages(message, systemPrompt, conversationHistory, attachedFile);

      const response = await model.invoke(messages);

      logger.info(`Multimodal LLM response generated using model: ${multimodalModel}`);
      return response.content;
    } catch (error) {
      logger.error('Error generating multimodal LLM response:', error);
      throw new Error('Failed to generate multimodal response from LLM');
    }
  }

  /**
   * Generate streaming chat response
   * @param {string} message - User message
   * @param {Object} res - Express response object
   * @param {string} [modelName] - Model name to use
   * @param {string} [systemPrompt] - System prompt
   * @param {Array} [conversationHistory] - Previous conversation
   * @param {Object} [metadata] - Additional metadata
   * @param {Function} [onComplete] - Callback when response is complete
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Promise<string>} Full response text
   */
  static async generateStreamingResponse(
    message,
    res,
    modelName,
    systemPrompt,
    conversationHistory,
    metadata,
    onComplete,
    attachedFile
  ) {
    try {
      // Use multimodal model if file is attached and contains images
      const shouldUseMultimodal = attachedFile && (attachedFile.type === 'image');
      const selectedModel = shouldUseMultimodal && modelName && this.supportsMultimodal(modelName)
        ? modelName
        : shouldUseMultimodal
          ? LLM_MODELS.OPENAI.GPT_4_VISION_PREVIEW
          : modelName;

      const model = this.getModel(selectedModel);
      const messages = this.buildMessages(message, systemPrompt, conversationHistory, attachedFile);

      // Set up SSE headers
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      let fullResponse = '';

      // Send initial event with metadata
      res.write(`data: ${JSON.stringify({
        type: 'start',
        message: 'Response started',
        metadata: metadata || {},
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Stream the response
      const stream = await model.stream(messages);

      for await (const chunk of stream) {
        const content = chunk.content;
        if (content) {
          fullResponse += content;
          res.write(`data: ${JSON.stringify({
            type: 'chunk',
            content: content,
            timestamp: new Date().toISOString()
          })}\n\n`);
        }
      }

      // Call completion callback if provided
      let finalMetadata = metadata || {};
      if (onComplete) {
        try {
          const callbackResult = await onComplete(fullResponse);
          finalMetadata = { ...finalMetadata, ...callbackResult };
        } catch (error) {
          logger.error('Error in onComplete callback:', error);
        }

      }      // Send completion event with updated metadata
      res.write(`data: ${JSON.stringify({
        type: 'complete',
        fullResponse: fullResponse,
        metadata: finalMetadata,
        timestamp: new Date().toISOString()
      })}\n\n`);

      res.end();

      logger.info(`LLM streaming response completed using model: ${modelName || 'default'}`);
      return fullResponse;
    } catch (error) {
      logger.error('Error generating streaming LLM response:', error);

      // Send error event
      res.write(`data: ${JSON.stringify({
        type: 'error',
        error: 'Failed to generate response from LLM',
        timestamp: new Date().toISOString()
      })}\n\n`);
      res.end();

      throw new Error('Failed to generate streaming response from LLM');
    }
}

  /**
   * Build messages array for LLM input with multimodal support
   * @param {string} message - User message
   * @param {string} [systemPrompt] - System prompt
   * @param {Array} [conversationHistory] - Previous conversation
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Array} Messages array for LLM
   */
  static buildMessages(
    message,
    systemPrompt,
    conversationHistory,
    attachedFile
  ) {
    const messages = [];

    // Add system message if provided, with file context if applicable
    if (systemPrompt) {
      let enhancedSystemPrompt = systemPrompt;
      if (attachedFile) {
        enhancedSystemPrompt += this.getSystemPromptAddition(attachedFile);
      }
      messages.push(new SystemMessage(enhancedSystemPrompt));
    } else {
      let defaultPrompt = 'You are a helpful assistant. Provide accurate, helpful, and concise responses to user questions. Use proper delimiters for Latex Expressions($...$ for inline math and $$...$$ for block math).';
      if (attachedFile) {
        defaultPrompt += this.getSystemPromptAddition(attachedFile);
      }
      messages.push(new SystemMessage(defaultPrompt));
    }

    // Add conversation history
    if (conversationHistory && conversationHistory.length > 0) {
      conversationHistory.forEach(msg => {
        if (msg.role === 'user') {
          messages.push(new HumanMessage(msg.content));
        } else {
          messages.push(new SystemMessage(msg.content));
        }
      });
    }

    logger.info('Conversation history:', systemPrompt);

    // Add current user message with multimodal content if applicable
    if (attachedFile && attachedFile.type === 'image') {
      // For images, create multimodal message
      const textContent = {
        type: 'text',
        text: message
      };

      const imageContent = Array.isArray(attachedFile.content)
        ? attachedFile.content
        : [attachedFile.content];

      const content = [textContent, ...imageContent];
      messages.push(new HumanMessage({ content: content }));
    } else if (attachedFile && (attachedFile.type === 'document' || attachedFile.type === 'text')) {
      // For documents and text, append content to message
      const enhancedMessage = `${message}\n\n--- Attached File Content ---\n${attachedFile.content}`;
      messages.push(new HumanMessage(enhancedMessage));
    } else {
      // Regular text message
      messages.push(new HumanMessage(message));
    }

    return messages;
  }

  /**
   * Get system prompt addition based on file type
   * @param {Object} processedFile - Processed file object
   * @returns {string} System prompt addition
   */
  static getSystemPromptAddition(processedFile) {
    const { type, metadata } = processedFile;

    switch (type) {
      case 'image':
        return `\n\nThe user has attached an image file (${metadata.originalName}). Please analyze the image content and respond accordingly.`;

      case 'document':
        if (metadata.mimeType === 'application/pdf') {
          return `\n\nThe user has attached a PDF document (${metadata.originalName}) which has been converted to images. Please analyze the document content and respond accordingly.`;
        } else if (metadata.mimeType.includes('word')) {
          return `\n\nThe user has attached a Word document (${metadata.originalName}) with the following content converted to markdown format. Please analyze the document and respond accordingly.`;
        } else if (metadata.mimeType.includes('sheet')) {
          return `\n\nThe user has attached an Excel spreadsheet (${metadata.originalName}) with the following data converted to markdown table format. Please analyze the data and respond accordingly.`;
        }
        return `\n\nThe user has attached a document (${metadata.originalName}). Please analyze the content and respond accordingly.`;

      case 'text':
        return `\n\nThe user has attached a text file (${metadata.originalName}) with the following content. Please analyze the text and respond accordingly.`;

      default:
        return `\n\nThe user has attached a file (${metadata.originalName}). Please analyze the content and respond accordingly.`;
    }
  }

  /**
   * Check if model supports multimodal inputs
   * @param {string} modelName - Model name to check
   * @returns {boolean} Whether model supports multimodal inputs
   */
  static supportsMultimodal(modelName) {
    const multimodalModels = [
      'gpt-4-vision-preview',
      'gpt-4o',
      'gpt-4o-mini'
    ];
    return multimodalModels.includes(modelName);
  }

  /**
   * Detect LLM provider from model name
   * @param {string} modelName - Model name
   * @returns {string} Provider name
   */
  static detectProvider(modelName) {
    const openAIModels = Object.values(LLM_MODELS.OPENAI);
    const anthropicModels = Object.values(LLM_MODELS.ANTHROPIC);

    if (openAIModels.includes(modelName)) {
      return 'OPENAI';
    }

    if (anthropicModels.includes(modelName)) {
      return 'ANTHROPIC';
    }

    // Default fallback based on model name patterns
    if (modelName.includes('gpt') || modelName.includes('openai')) {
      return 'OPENAI';
    }

    if (modelName.includes('claude') || modelName.includes('anthropic')) {
      return 'ANTHROPIC';
    }

    throw new Error(`Cannot detect provider for model: ${modelName}`);
  }

  /**
   * Get available models
   * @returns {Array<string>} Array of available model names
   */
  static getAvailableModels() {
    return Array.from(this.models.keys());
  }

  /**
   * Check if model is available
   * @param {string} modelName - Model name to check
   * @returns {boolean} Whether model is available
   */
  static isModelAvailable(modelName) {
    return this.models.has(modelName);
  }

  /**
   * Get model statistics
   * @returns {Object} Model statistics
   */
  static getModelStats() {
    return {
      totalModels: this.models.size,
      availableModels: this.getAvailableModels(),
      openAIModels: Object.values(LLM_MODELS.OPENAI),
      anthropicModels: Object.values(LLM_MODELS.ANTHROPIC),
    };
  }
}
